<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.GuoBiaoDietitian" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/green_40</item>
        <item name="colorPrimaryDark">@color/green_40</item>
        <item name="colorAccent">@color/orange_40</item>
        <item name="android:statusBarColor">@color/green_40</item>
        <item name="android:windowBackground">@color/light_background</item>
    </style>

    <!-- Splash Screen theme for GuoBiaoDietitian -->
    <style name="Theme.GuoBiaoDietitian.SplashScreen" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/green_40</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_launcher_foreground</item>
        <item name="postSplashScreenTheme">@style/Theme.GuoBiaoDietitian</item>
    </style>
</resources>
