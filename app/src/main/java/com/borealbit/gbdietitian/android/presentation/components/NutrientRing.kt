package com.borealbit.gbdietitian.android.presentation.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.borealbit.gbdietitian.android.presentation.theme.GuoBiaoDietitianTheme
import com.borealbit.gbdietitian.android.presentation.theme.NutrientDanger
import com.borealbit.gbdietitian.android.presentation.theme.NutrientGood
import com.borealbit.gbdietitian.android.presentation.theme.NutrientWarning
import kotlin.math.min

@Composable
fun NutrientRing(
    current: Double,
    target: Double,
    label: String,
    unit: String = "g",
    modifier: Modifier = Modifier,
    size: Dp = 120.dp,
    strokeWidth: Dp = 12.dp,
    animationDuration: Int = 1000
) {
    val progress = (current / target).toFloat().coerceIn(0f, 2f) // 允许超标显示
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(durationMillis = animationDuration),
        label = "progress_animation"
    )

    val progressColor = when {
        progress <= 0.7f -> NutrientGood
        progress <= 1.0f -> NutrientWarning
        else -> NutrientDanger
    }

    val surfaceVariantColor = MaterialTheme.colorScheme.surfaceVariant

    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier.size(size)
    ) {
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val canvasSize = min(this.size.width, this.size.height)
            val radius = (canvasSize - strokeWidth.toPx()) / 2
            val center = androidx.compose.ui.geometry.Offset(
                canvasSize / 2,
                canvasSize / 2
            )

            // 背景圆环
            drawCircle(
                color = surfaceVariantColor,
                radius = radius,
                center = center,
                style = Stroke(width = strokeWidth.toPx(), cap = StrokeCap.Round)
            )

            // 进度圆环
            val sweepAngle = 360f * min(animatedProgress, 1f)
            if (sweepAngle > 0) {
                drawArc(
                    color = progressColor,
                    startAngle = -90f,
                    sweepAngle = sweepAngle,
                    useCenter = false,
                    style = Stroke(width = strokeWidth.toPx(), cap = StrokeCap.Round)
                )
            }

            // 超标指示器
            if (animatedProgress > 1f) {
                val overSweepAngle = 360f * (animatedProgress - 1f).coerceAtMost(1f)
                drawArc(
                    color = NutrientDanger.copy(alpha = 0.3f),
                    startAngle = -90f + sweepAngle,
                    sweepAngle = overSweepAngle,
                    useCenter = false,
                    style = Stroke(width = strokeWidth.toPx() * 0.5f, cap = StrokeCap.Round)
                )
            }
        }

        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "${current.toInt()}",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                ),
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = "/ ${target.toInt()}$unit",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = label,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun NutrientRingPreview() {
    GuoBiaoDietitianTheme {
        Row(
            modifier = Modifier.padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            NutrientRing(
                current = 45.0,
                target = 60.0,
                label = "蛋白质",
                unit = "g"
            )
            NutrientRing(
                current = 180.0,
                target = 200.0,
                label = "碳水",
                unit = "g"
            )
            NutrientRing(
                current = 85.0,
                target = 70.0,
                label = "脂肪",
                unit = "g"
            )
        }
    }
}
