/*
 * Copyright 2025 BorealBit
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.gbdietitian.android.presentation.navigation

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.core.EaseInOut
import androidx.compose.animation.core.EaseOutExpo
import androidx.compose.animation.core.FiniteAnimationSpec
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideOutVertically
import androidx.compose.ui.unit.IntOffset

/**
 * 导航动画系统
 * 
 * 学习自 EdgeMind 的高级动画配置
 * 为营养师应用提供流畅的导航体验
 */

/**
 * 动画配置常量
 */
object AnimationConfig {
    // 标准动画时长
    const val STANDARD_DURATION_MS = 300
    const val LONG_DURATION_MS = 500
    const val SHORT_DURATION_MS = 150
    
    // 动画延迟
    const val ENTER_DELAY_MS = 50
    const val EXIT_DELAY_MS = 0
    
    // 缓动函数
    val STANDARD_EASING = EaseInOut
    val ENTER_EASING = EaseOutExpo
    val EXIT_EASING = EaseInOut
    
    // 滑动距离
    const val SLIDE_DISTANCE = 300
    const val SLIDE_DISTANCE_SMALL = 150
}

/**
 * 动画规格创建函数
 */
private fun standardTween(): FiniteAnimationSpec<IntOffset> {
    return tween(
        durationMillis = AnimationConfig.STANDARD_DURATION_MS,
        easing = AnimationConfig.STANDARD_EASING
    )
}

private fun enterTween(): FiniteAnimationSpec<IntOffset> {
    return tween(
        durationMillis = AnimationConfig.STANDARD_DURATION_MS,
        easing = AnimationConfig.ENTER_EASING,
        delayMillis = AnimationConfig.ENTER_DELAY_MS
    )
}

private fun exitTween(): FiniteAnimationSpec<IntOffset> {
    return tween(
        durationMillis = AnimationConfig.STANDARD_DURATION_MS,
        easing = AnimationConfig.EXIT_EASING,
        delayMillis = AnimationConfig.EXIT_DELAY_MS
    )
}

private fun fadeTween(duration: Int = AnimationConfig.STANDARD_DURATION_MS): FiniteAnimationSpec<Float> {
    return tween(
        durationMillis = duration,
        easing = AnimationConfig.STANDARD_EASING
    )
}

/**
 * 标准导航动画
 */
object NavigationAnimations {
    
    /**
     * 水平滑动进入 (从右到左)
     */
    fun AnimatedContentTransitionScope<*>.slideInFromRight(): EnterTransition {
        return slideIntoContainer(
            animationSpec = enterTween(),
            towards = AnimatedContentTransitionScope.SlideDirection.Left
        )
    }
    
    /**
     * 水平滑动退出 (从左到右)
     */
    fun AnimatedContentTransitionScope<*>.slideOutToRight(): ExitTransition {
        return slideOutOfContainer(
            animationSpec = exitTween(),
            towards = AnimatedContentTransitionScope.SlideDirection.Right
        )
    }
    
    /**
     * 水平滑动进入 (从左到右)
     */
    fun AnimatedContentTransitionScope<*>.slideInFromLeft(): EnterTransition {
        return slideIntoContainer(
            animationSpec = enterTween(),
            towards = AnimatedContentTransitionScope.SlideDirection.Right
        )
    }
    
    /**
     * 水平滑动退出 (从右到左)
     */
    fun AnimatedContentTransitionScope<*>.slideOutToLeft(): ExitTransition {
        return slideOutOfContainer(
            animationSpec = exitTween(),
            towards = AnimatedContentTransitionScope.SlideDirection.Left
        )
    }
    
    /**
     * 垂直滑动进入 (从下到上)
     */
    fun slideInFromBottom(): EnterTransition {
        return slideInVertically(
            animationSpec = standardTween(),
            initialOffsetY = { it }
        )
    }
    
    /**
     * 垂直滑动退出 (从上到下)
     */
    fun slideOutToBottom(): ExitTransition {
        return slideOutVertically(
            animationSpec = standardTween(),
            targetOffsetY = { it }
        )
    }
    
    /**
     * 垂直滑动进入 (从上到下)
     */
    fun slideInFromTop(): EnterTransition {
        return slideInVertically(
            animationSpec = standardTween(),
            initialOffsetY = { -it }
        )
    }
    
    /**
     * 垂直滑动退出 (从下到上)
     */
    fun slideOutToTop(): ExitTransition {
        return slideOutVertically(
            animationSpec = standardTween(),
            targetOffsetY = { -it }
        )
    }
    
    /**
     * 淡入动画
     */
    fun fadeIn(): EnterTransition {
        return fadeIn(animationSpec = fadeTween())
    }
    
    /**
     * 淡出动画
     */
    fun fadeOut(): ExitTransition {
        return fadeOut(animationSpec = fadeTween())
    }
    
    /**
     * 缩放进入动画
     */
    fun scaleIn(): EnterTransition {
        return scaleIn(
            animationSpec = tween(
                durationMillis = AnimationConfig.STANDARD_DURATION_MS,
                easing = AnimationConfig.ENTER_EASING
            ),
            initialScale = 0.8f
        ) + fadeIn(animationSpec = fadeTween())
    }
    
    /**
     * 缩放退出动画
     */
    fun scaleOut(): ExitTransition {
        return scaleOut(
            animationSpec = tween(
                durationMillis = AnimationConfig.SHORT_DURATION_MS,
                easing = AnimationConfig.EXIT_EASING
            ),
            targetScale = 0.8f
        ) + fadeOut(animationSpec = fadeTween(AnimationConfig.SHORT_DURATION_MS))
    }
    
    /**
     * 组合动画：滑动 + 淡入淡出
     */
    fun AnimatedContentTransitionScope<*>.slideAndFadeIn(): EnterTransition {
        return slideInFromRight() + fadeIn()
    }
    
    fun AnimatedContentTransitionScope<*>.slideAndFadeOut(): ExitTransition {
        return slideOutToLeft() + fadeOut()
    }
}

/**
 * 特定场景的动画配置
 */
object SceneAnimations {
    
    /**
     * 底部导航切换动画
     */
    fun bottomNavEnter(): EnterTransition {
        return NavigationAnimations.fadeIn()
    }
    
    fun bottomNavExit(): ExitTransition {
        return NavigationAnimations.fadeOut()
    }
    
    /**
     * 模态弹窗动画 (如设置页面)
     */
    fun modalEnter(): EnterTransition {
        return NavigationAnimations.slideInFromBottom() + NavigationAnimations.fadeIn()
    }
    
    fun modalExit(): ExitTransition {
        return NavigationAnimations.slideOutToBottom() + NavigationAnimations.fadeOut()
    }
    
    /**
     * 详情页面动画 (如食物详情)
     */
    fun AnimatedContentTransitionScope<*>.detailEnter(): EnterTransition {
        return with(NavigationAnimations) { slideInFromRight() } + NavigationAnimations.fadeIn()
    }

    fun AnimatedContentTransitionScope<*>.detailExit(): ExitTransition {
        return with(NavigationAnimations) { slideOutToRight() } + NavigationAnimations.fadeOut()
    }
    
    /**
     * 扫描页面动画
     */
    fun scanEnter(): EnterTransition {
        return NavigationAnimations.scaleIn()
    }
    
    fun scanExit(): ExitTransition {
        return NavigationAnimations.scaleOut()
    }
    
    /**
     * AI 聊天页面动画
     */
    fun AnimatedContentTransitionScope<*>.chatEnter(): EnterTransition {
        return with(NavigationAnimations) { slideInFromRight() } + NavigationAnimations.fadeIn()
    }

    fun AnimatedContentTransitionScope<*>.chatExit(): ExitTransition {
        return with(NavigationAnimations) { slideOutToRight() } + NavigationAnimations.fadeOut()
    }
    
    /**
     * 分析报告页面动画
     */
    fun analysisEnter(): EnterTransition {
        return NavigationAnimations.slideInFromTop() + NavigationAnimations.fadeIn()
    }
    
    fun analysisExit(): ExitTransition {
        return NavigationAnimations.slideOutToTop() + NavigationAnimations.fadeOut()
    }
}

/**
 * 动画选择器
 * 根据导航路径自动选择合适的动画
 */
object AnimationSelector {
    
    fun getEnterAnimation(
        scope: AnimatedContentTransitionScope<*>,
        targetRoute: String
    ): EnterTransition {
        return with(scope) {
            when {
                targetRoute.contains("scan") -> SceneAnimations.scanEnter()
                targetRoute.contains("settings") -> SceneAnimations.modalEnter()
                targetRoute.contains("goals") -> SceneAnimations.modalEnter()
                targetRoute.contains("analysis") -> SceneAnimations.analysisEnter()
                targetRoute.contains("detail") -> with(SceneAnimations) { detailEnter() }
                targetRoute.contains("chat") -> with(SceneAnimations) { chatEnter() }
                else -> with(NavigationAnimations) { slideInFromRight() }
            }
        }
    }

    fun getExitAnimation(
        scope: AnimatedContentTransitionScope<*>,
        targetRoute: String
    ): ExitTransition {
        return with(scope) {
            when {
                targetRoute.contains("scan") -> SceneAnimations.scanExit()
                targetRoute.contains("settings") -> SceneAnimations.modalExit()
                targetRoute.contains("goals") -> SceneAnimations.modalExit()
                targetRoute.contains("analysis") -> SceneAnimations.analysisExit()
                targetRoute.contains("detail") -> with(SceneAnimations) { detailExit() }
                targetRoute.contains("chat") -> with(SceneAnimations) { chatExit() }
                else -> with(NavigationAnimations) { slideOutToLeft() }
            }
        }
    }
}
