package com.borealbit.gbdietitian.android.presentation

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.borealbit.gbdietitian.android.presentation.components.NutrientRing
import com.borealbit.gbdietitian.android.presentation.components.NutritionCard
import com.borealbit.gbdietitian.android.presentation.theme.GuoBiaoDietitianTheme
import dagger.hilt.android.AndroidEntryPoint

@OptIn(ExperimentalMaterial3Api::class)
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            GuoBiaoDietitianTheme {
                Scaffold(
                    modifier = Modifier.fillMaxSize(),
                    topBar = {
                        TopAppBar(
                            title = {
                                Text(
                                    "国标营养师",
                                    fontWeight = FontWeight.Bold
                                )
                            },
                            colors = TopAppBarDefaults.topAppBarColors(
                                containerColor = MaterialTheme.colorScheme.primary,
                                titleContentColor = MaterialTheme.colorScheme.onPrimary
                            )
                        )
                    },
                    bottomBar = {
                        BottomNavigationBar()
                    }
                ) { innerPadding ->
                    Surface(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        HomeScreen()
                    }
                }
            }
        }
    }
}

@Composable
fun HomeScreen() {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // 欢迎卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "欢迎使用国标营养师！",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "基于中国居民膳食指南2022和GB 28050-2011标准的智能营养管理",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
        }

        item {
            // 今日营养摄入概览
            Text(
                text = "今日营养摄入",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        }

        item {
            // 营养环形图
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                NutrientRing(
                    current = 1650.0,
                    target = 2000.0,
                    label = "卡路里",
                    unit = "kcal"
                )
                NutrientRing(
                    current = 45.0,
                    target = 60.0,
                    label = "蛋白质",
                    unit = "g"
                )
                NutrientRing(
                    current = 85.0,
                    target = 70.0,
                    label = "脂肪",
                    unit = "g"
                )
            }
        }

        item {
            // 营养详情卡片
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                NutritionCard(
                    title = "碳水化合物",
                    current = 180.0,
                    target = 250.0,
                    unit = "g"
                )
                NutritionCard(
                    title = "钠",
                    current = 2800.0,
                    target = 2300.0,
                    unit = "mg",
                    icon = Icons.Default.Warning
                )
                NutritionCard(
                    title = "膳食纤维",
                    current = 15.0,
                    target = 25.0,
                    unit = "g"
                )
            }
        }

        item {
            // 快速操作按钮
            Text(
                text = "快速记录",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        }

        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                QuickActionButton(
                    icon = Icons.Default.Camera,
                    text = "拍照识别",
                    modifier = Modifier.weight(1f),
                    onClick = { /* TODO: 打开相机 */ }
                )
                QuickActionButton(
                    icon = Icons.Default.QrCodeScanner,
                    text = "扫描条码",
                    modifier = Modifier.weight(1f),
                    onClick = { /* TODO: 打开条码扫描 */ }
                )
            }
        }

        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                QuickActionButton(
                    icon = Icons.Default.Edit,
                    text = "手动输入",
                    modifier = Modifier.weight(1f),
                    onClick = { /* TODO: 打开手动输入 */ }
                )
                QuickActionButton(
                    icon = Icons.Default.Mic,
                    text = "语音输入",
                    modifier = Modifier.weight(1f),
                    onClick = { /* TODO: 打开语音输入 */ }
                )
            }
        }
    }
}

@Composable
fun BottomNavigationBar() {
    NavigationBar {
        NavigationBarItem(
            icon = { Icon(Icons.Default.Home, contentDescription = null) },
            label = { Text("首页") },
            selected = true,
            onClick = { /* TODO: 导航到首页 */ }
        )
        NavigationBarItem(
            icon = { Icon(Icons.Default.Restaurant, contentDescription = null) },
            label = { Text("记录") },
            selected = false,
            onClick = { /* TODO: 导航到食物记录 */ }
        )
        NavigationBarItem(
            icon = { Icon(Icons.Default.Analytics, contentDescription = null) },
            label = { Text("分析") },
            selected = false,
            onClick = { /* TODO: 导航到营养分析 */ }
        )
        NavigationBarItem(
            icon = { Icon(Icons.Default.Chat, contentDescription = null) },
            label = { Text("AI助手") },
            selected = false,
            onClick = { /* TODO: 导航到AI助手 */ }
        )
        NavigationBarItem(
            icon = { Icon(Icons.Default.Person, contentDescription = null) },
            label = { Text("我的") },
            selected = false,
            onClick = { /* TODO: 导航到个人档案 */ }
        )
    }
}

@Composable
fun QuickActionButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier.height(80.dp),
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = MaterialTheme.colorScheme.primary
        )
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(24.dp)
            )
            Text(
                text = text,
                style = MaterialTheme.typography.labelMedium
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun HomeScreenPreview() {
    GuoBiaoDietitianTheme {
        HomeScreen()
    }
}
